#!/usr/bin/env python3
"""
测试更新后的data_loader是否能正确识别Isolation Forest异常检测结果
"""

import os
import sys

def test_file_priority():
    """测试文件优先级识别"""
    print("🧪 测试data_loader文件优先级识别")
    print("=" * 50)
    
    # 检查当前存在的异常检测结果文件
    train_files = [
        "train_isolation_outliers.csv",
        "data/train_isolation_outliers.csv",
        "train_outliers.csv",
        "data/train_outliers.csv",
        "train_combined_outliers.csv",
        "data/train_combined_outliers.csv",
        "train_kmeans_outliers.csv",
        "data/train_kmeans_outliers.csv"
    ]
    
    val_files = [
        "val_isolation_outliers.csv",
        "data/val_isolation_outliers.csv",
        "val_outliers.csv",
        "data/val_outliers.csv",
        "val_combined_outliers.csv",
        "data/val_combined_outliers.csv",
        "val_kmeans_outliers.csv",
        "data/val_kmeans_outliers.csv"
    ]
    
    print("📂 检查训练集异常文件:")
    train_found = []
    for file in train_files:
        if os.path.exists(file):
            train_found.append(file)
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
    
    print("\n📂 检查验证集异常文件:")
    val_found = []
    for file in val_files:
        if os.path.exists(file):
            val_found.append(file)
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
    
    # 模拟data_loader的选择逻辑
    print("\n🎯 data_loader将选择的文件:")
    
    # 训练集选择
    if train_found:
        selected_train = train_found[0]  # 第一个找到的文件
        if "isolation" in selected_train:
            print(f"  训练集: {selected_train} (Isolation Forest - 推荐)")
        elif "outliers.csv" in selected_train and "combined" not in selected_train and "kmeans" not in selected_train:
            print(f"  训练集: {selected_train} (二次筛选)")
        elif "combined" in selected_train:
            print(f"  训练集: {selected_train} (组合筛选)")
        else:
            print(f"  训练集: {selected_train} (K-means)")
    else:
        print("  训练集: 无异常文件，将加载所有数据")
    
    # 验证集选择
    if val_found:
        selected_val = val_found[0]  # 第一个找到的文件
        if "isolation" in selected_val:
            print(f"  验证集: {selected_val} (Isolation Forest - 推荐)")
        elif "outliers.csv" in selected_val and "combined" not in selected_val and "kmeans" not in selected_val:
            print(f"  验证集: {selected_val} (二次筛选)")
        elif "combined" in selected_val:
            print(f"  验证集: {selected_val} (组合筛选)")
        else:
            print(f"  验证集: {selected_val} (K-means)")
    else:
        print("  验证集: 无异常文件，将加载所有数据")
    
    return train_found, val_found

def test_data_loader_import():
    """测试data_loader导入"""
    try:
        from data.data_loader import get_source_data
        print("✅ data_loader导入成功")
        return True
    except Exception as e:
        print(f"❌ data_loader导入失败: {e}")
        return False

def show_recommendations():
    """显示使用建议"""
    print("\n💡 使用建议:")
    print("=" * 30)
    print("1. 运行异常检测生成Isolation Forest结果:")
    print("   python run_outlier_detection.py")
    print("   选择选项2 (仅使用Isolation Forest检测)")
    print()
    print("2. 运行主训练脚本:")
    print("   python main.py")
    print("   data_loader会自动使用最新的Isolation Forest结果")
    print()
    print("3. 文件优先级 (从高到低):")
    print("   - train/val_isolation_outliers.csv (推荐)")
    print("   - train/val_outliers.csv (二次筛选)")
    print("   - train/val_combined_outliers.csv (组合)")
    print("   - train/val_kmeans_outliers.csv (K-means)")

if __name__ == "__main__":
    print("🔧 Data Loader更新测试")
    print("=" * 40)
    
    # 测试导入
    if not test_data_loader_import():
        sys.exit(1)
    
    # 测试文件优先级
    train_found, val_found = test_file_priority()
    
    # 显示建议
    show_recommendations()
    
    print(f"\n✅ 测试完成!")
    if any("isolation" in f for f in train_found + val_found):
        print("🎉 检测到Isolation Forest结果文件，data_loader将优先使用!")
    else:
        print("⚠️ 未检测到Isolation Forest结果文件，建议先运行异常检测")
