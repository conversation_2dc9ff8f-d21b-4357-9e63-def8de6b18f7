# 异常检测参数调整指南

## 概述

已经成功修改了异常检测系统，现在主要使用 **Isolation Forest** 进行异常检测，并且可以方便地调整筛选强度。

## 主要修改

### 1. 降低了默认筛选强度
- **原始设置**: `contamination=0.08` (8%异常比例)
- **新设置**: `contamination=0.05` (5%异常比例)
- **效果**: 筛选的异常样本数量适度减少

### 2. 新增了纯Isolation Forest检测选项
运行 `python run_outlier_detection.py` 时的新选项：
```
🔧 选择异常检测方法:
1. 仅使用K-means聚类检测
2. 仅使用Isolation Forest检测 (推荐) ← 新增推荐选项
3. 使用K-means + Isolation Forest二次筛选
4. 运行多种方法并对比结果
```

### 3. 添加了配置文件系统
- `outlier_detection_config.py`: 集中管理所有参数
- `adjust_contamination.py`: 快速调整contamination参数的工具

## 如何调整筛选强度

### 方法1: 使用快速调整工具 (推荐)
```bash
python adjust_contamination.py
```
然后按提示选择新的contamination值。

### 方法2: 直接修改配置文件
编辑 `outlier_detection_config.py` 文件中的：
```python
ISOLATION_FOREST_CONFIG = {
    'contamination': 0.04,  # 修改这个值
    # ...
}
```

### 方法3: 在代码中临时修改
在调用异常检测函数时直接指定参数：
```python
isolation_forest_outlier_detection(
    folder_path=train_path,
    behavioral_features=behavioral_features,
    contamination=0.03,  # 临时使用更严格的设置
    output_csv="train_outliers.csv"
)
```

## Contamination参数指南

| 值 | 异常比例 | 筛选强度 | 适用场景 |
|---|---------|---------|----------|
| 0.01 | 1% | 非常严格 | 只想筛选最明显的异常 |
| 0.02 | 2% | 严格 | 高质量数据要求 |
| 0.03 | 3% | 较严格 | 平衡质量和数量 |
| 0.04 | 4% | 适中 | 平衡质量和数量 |
| **0.05** | **5%** | **稍宽松** | **当前默认设置** |
| 0.06 | 6% | 宽松 | 数据量优先 |
| 0.08 | 8% | 较宽松 | 原始设置 |
| 0.10 | 10% | 很宽松 | 筛选较多异常 |

## 使用流程

### 1. 运行异常检测
```bash
python run_outlier_detection.py
```
选择选项2 (仅使用Isolation Forest检测)

### 2. 查看结果
检查生成的文件：
- `train_isolation_outliers.csv`: 训练集异常文件列表
- `val_isolation_outliers.csv`: 验证集异常文件列表
- `*_detailed.csv`: 详细分析结果

### 3. 如果筛选太多/太少
使用调整工具：
```bash
python adjust_contamination.py
```
或直接修改 `outlier_detection_config.py`

### 4. 重新运行检测
```bash
python run_outlier_detection.py
```

## 预期效果

使用新的设置 (`contamination=0.05`)，预期：
- **训练集**: 从587个异常减少到约300-400个
- **验证集**: 从77个异常减少到约40-50个
- **总体**: 异常比例从8%降低到5%

## 文件说明

- `run_outlier_detection.py`: 主运行脚本
- `data/kmeans_isolation_outlier_detection.py`: 核心检测算法
- `outlier_detection_config.py`: 参数配置文件
- `adjust_contamination.py`: 参数调整工具
- `test_isolation_forest.py`: 测试脚本

## 故障排除

### 如果筛选结果仍然太多
1. 降低contamination值到0.02或0.03
2. 检查特征选择是否合适
3. 考虑数据预处理质量

### 如果筛选结果太少
1. 提高contamination值到0.05或0.06
2. 检查数据是否确实存在异常

### 如果遇到错误
1. 检查数据路径是否正确
2. 确保所有依赖包已安装
3. 查看详细错误信息

## 建议

1. **首次使用**: 建议从contamination=0.05开始
2. **数据质量优先**: 使用0.02-0.03
3. **数据量优先**: 使用0.05-0.06
4. **对比测试**: 使用选项4对比不同方法的效果
