#!/usr/bin/env python3
"""
验证data_loader更新 - 不依赖外部库
"""

import os

def check_isolation_files():
    """检查Isolation Forest结果文件"""
    print("🔍 检查Isolation Forest异常检测结果文件")
    print("=" * 50)
    
    files_to_check = [
        "train_isolation_outliers.csv",
        "val_isolation_outliers.csv",
        "data/train_isolation_outliers.csv", 
        "data/val_isolation_outliers.csv"
    ]
    
    found_files = []
    for file in files_to_check:
        if os.path.exists(file):
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if len(lines) > 1:  # 有标题行和数据行
                        data_lines = len(lines) - 1
                        print(f"✅ {file} - {data_lines} 个异常样本")
                        found_files.append((file, data_lines))
                    else:
                        print(f"⚠️ {file} - 文件为空或只有标题")
            except Exception as e:
                print(f"❌ {file} - 读取失败: {e}")
        else:
            print(f"❌ {file} - 文件不存在")
    
    return found_files

def simulate_data_loader_selection():
    """模拟data_loader的文件选择逻辑"""
    print("\n🎯 模拟data_loader文件选择逻辑")
    print("=" * 40)
    
    # 训练集候选文件 (按优先级排序)
    train_candidates = [
        "train_isolation_outliers.csv",
        "data/train_isolation_outliers.csv",
        "data/train_outliers.csv",
        "train_outliers.csv",
        "data/train_combined_outliers.csv",
        "train_combined_outliers.csv",
        "data/train_kmeans_outliers.csv",
        "train_kmeans_outliers.csv"
    ]
    
    # 验证集候选文件 (按优先级排序)
    val_candidates = [
        "val_isolation_outliers.csv",
        "data/val_isolation_outliers.csv",
        "data/val_outliers.csv",
        "val_outliers.csv",
        "data/val_combined_outliers.csv",
        "val_combined_outliers.csv",
        "data/val_kmeans_outliers.csv",
        "val_kmeans_outliers.csv"
    ]
    
    # 选择训练集文件
    train_selected = None
    for file in train_candidates:
        if os.path.exists(file):
            train_selected = file
            break
    
    # 选择验证集文件
    val_selected = None
    for file in val_candidates:
        if os.path.exists(file):
            val_selected = file
            break
    
    # 显示选择结果
    print("📂 训练集:")
    if train_selected:
        if "isolation" in train_selected:
            print(f"  ✅ 将使用: {train_selected} (Isolation Forest - 推荐)")
        elif "outliers.csv" in train_selected and "combined" not in train_selected and "kmeans" not in train_selected:
            print(f"  ✅ 将使用: {train_selected} (二次筛选)")
        elif "combined" in train_selected:
            print(f"  ✅ 将使用: {train_selected} (组合筛选)")
        else:
            print(f"  ⚠️ 将使用: {train_selected} (K-means)")
    else:
        print("  ❌ 未找到异常文件，将加载所有训练数据")
    
    print("\n📂 验证集:")
    if val_selected:
        if "isolation" in val_selected:
            print(f"  ✅ 将使用: {val_selected} (Isolation Forest - 推荐)")
        elif "outliers.csv" in val_selected and "combined" not in val_selected and "kmeans" not in val_selected:
            print(f"  ✅ 将使用: {val_selected} (二次筛选)")
        elif "combined" in val_selected:
            print(f"  ✅ 将使用: {val_selected} (组合筛选)")
        else:
            print(f"  ⚠️ 将使用: {val_selected} (K-means)")
    else:
        print("  ❌ 未找到异常文件，将加载所有验证数据")
    
    return train_selected, val_selected

def show_summary():
    """显示总结信息"""
    print("\n📋 Data Loader更新总结")
    print("=" * 30)
    print("✅ 已更新data_loader.py文件优先级:")
    print("   1. train/val_isolation_outliers.csv (最高优先级)")
    print("   2. data/train/val_isolation_outliers.csv")
    print("   3. train/val_outliers.csv (二次筛选)")
    print("   4. train/val_combined_outliers.csv (组合)")
    print("   5. train/val_kmeans_outliers.csv (K-means)")
    print()
    print("💡 使用方法:")
    print("   1. 运行: python run_outlier_detection.py")
    print("   2. 选择选项2 (仅使用Isolation Forest检测)")
    print("   3. 运行: python main.py")
    print("   4. data_loader会自动使用Isolation Forest结果")

if __name__ == "__main__":
    print("🔧 验证Data Loader更新")
    print("=" * 40)
    
    # 检查文件
    found_files = check_isolation_files()
    
    # 模拟选择逻辑
    train_selected, val_selected = simulate_data_loader_selection()
    
    # 显示总结
    show_summary()
    
    print(f"\n✅ 验证完成!")
    
    if train_selected and "isolation" in train_selected and val_selected and "isolation" in val_selected:
        print("🎉 完美！data_loader将优先使用Isolation Forest结果")
        print("💡 现在可以直接运行main.py进行训练")
    elif found_files:
        print("✅ 检测到Isolation Forest结果文件")
        print("💡 data_loader已配置为优先使用这些文件")
    else:
        print("⚠️ 建议先运行异常检测生成Isolation Forest结果")
