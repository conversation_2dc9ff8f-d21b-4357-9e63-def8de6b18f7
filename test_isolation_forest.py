#!/usr/bin/env python3
"""
测试仅使用Isolation Forest的异常检测功能
"""

import os
import sys
import pandas as pd
import numpy as np
from data.kmeans_isolation_outlier_detection import isolation_forest_outlier_detection

def create_test_data(output_dir="test_data", num_samples=50):
    """创建测试数据"""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    print(f"📂 创建测试数据到: {output_dir}")
    
    # 创建正常数据
    for i in range(num_samples - 5):
        data = {
            'pose_Tx': np.random.normal(0, 1, 100),
            'pose_Ty': np.random.normal(0, 1, 100),
            'AU17_r': np.random.normal(0.5, 0.2, 100),
            'AU26_r': np.random.normal(0.3, 0.15, 100)
        }
        df = pd.DataFrame(data)
        df.to_csv(os.path.join(output_dir, f"normal_{i:03d}.csv"), index=False)
    
    # 创建异常数据
    for i in range(5):
        data = {
            'pose_Tx': np.random.normal(5, 2, 100),  # 异常的头部位置
            'pose_Ty': np.random.normal(-3, 1.5, 100),
            'AU17_r': np.random.normal(2.0, 0.5, 100),  # 异常的面部动作
            'AU26_r': np.random.normal(1.5, 0.3, 100)
        }
        df = pd.DataFrame(data)
        df.to_csv(os.path.join(output_dir, f"outlier_{i:03d}.csv"), index=False)
    
    print(f"✅ 创建了 {num_samples} 个测试样本 (包含5个异常样本)")
    return output_dir

def test_isolation_forest():
    """测试Isolation Forest异常检测"""
    print("🧪 测试Isolation Forest异常检测功能")
    print("=" * 50)
    
    # 创建测试数据
    test_dir = create_test_data()
    
    # 配置特征
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]
    
    try:
        # 测试不同的contamination参数
        contamination_values = [0.05, 0.1, 0.15, 0.2]
        
        for contamination in contamination_values:
            print(f"\n🔧 测试contamination={contamination}")
            print("-" * 30)
            
            result = isolation_forest_outlier_detection(
                folder_path=test_dir,
                behavioral_features=behavioral_features,
                contamination=contamination,
                output_csv=f"test_isolation_{contamination}.csv"
            )
            
            if result:
                outlier_count = len(result['final_outliers'])
                total_count = len(result['filenames'])
                print(f"📊 检测到异常: {outlier_count}/{total_count} ({outlier_count/total_count*100:.1f}%)")
                
                # 检查是否检测到了我们创建的异常样本
                outlier_files = result['final_outliers']
                detected_outliers = [f for f in outlier_files if f.startswith('outlier_')]
                print(f"🎯 检测到的真实异常: {len(detected_outliers)}/5")
                
                if detected_outliers:
                    print(f"   检测到的异常文件: {detected_outliers}")
            else:
                print("❌ 检测失败")
        
        print(f"\n✅ 测试完成!")
        print(f"📄 生成的文件:")
        for contamination in contamination_values:
            csv_file = f"test_isolation_{contamination}.csv"
            detailed_file = f"test_isolation_{contamination}_detailed.csv"
            if os.path.exists(csv_file):
                print(f"   - {csv_file}")
            if os.path.exists(detailed_file):
                print(f"   - {detailed_file}")
        
        # 清理测试数据
        import shutil
        if os.path.exists(test_dir):
            shutil.rmtree(test_dir)
            print(f"🧹 清理测试数据: {test_dir}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_isolation_forest()
