{"data_paths": {"train_folder": "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train", "validation_folder": "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation", "test_folder": "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Test"}, "behavioral_features": ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"], "kmeans_params": {"n_clusters": "auto", "distance_threshold": 2.5, "random_state": 42, "n_init": 10, "max_iter": 300}, "isolation_forest_params": {"contamination": 0.05, "n_estimators": 200, "max_samples": "auto", "random_state": 42, "n_jobs": -1}, "combination_strategy": {"method": "weighted", "kmeans_weight": 0.6, "isolation_weight": 0.4, "use_union": false, "use_intersection": false}, "output_settings": {"save_detailed_results": true, "save_visualization": true, "save_feature_importance": true, "output_directory": "outlier_analysis"}, "advanced_features": {"use_statistical_features": true, "statistical_features": ["mean", "std", "median", "q25", "q75", "range"], "enable_pca_visualization": true, "pca_components": 2}, "quality_control": {"min_samples_per_cluster": 5, "max_contamination": 0.2, "min_contamination": 0.01, "silhouette_threshold": 0.3}, "logging": {"log_level": "INFO", "save_logs": true, "log_file": "outlier_detection.log"}}