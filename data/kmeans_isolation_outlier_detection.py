import os
import pandas as pd
import numpy as np
from tqdm import tqdm
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import silhouette_score
from sklearn.ensemble import IsolationForest
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def isolation_forest_outlier_detection(folder_path, behavioral_features,
                                       contamination=0.08, output_csv="outliers.csv"):
    """
    仅使用Isolation Forest进行异常检测。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径
        behavioral_features (list): 要提取的行为特征列
        contamination (float): Isolation Forest异常比例 (0.05-0.2)
        output_csv (str): 输出的异常文件列表 CSV

    返回:
        dict: 包含检测结果的字典
    """
    print(f"🚀 开始Isolation Forest异常检测: {os.path.basename(folder_path)}")
    print(f"📋 使用特征: {behavioral_features}")

    # 第一步：数据加载和预处理
    all_vectors = []
    filenames = []

    print("📂 加载数据文件...")
    for fname in tqdm(os.listdir(folder_path), desc="加载数据"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)

            # 检查特征列是否存在
            missing_features = [f for f in behavioral_features if f not in df.columns]
            if missing_features:
                print(f"⚠️ {fname} 缺少特征: {missing_features}")
                continue

            # 提取特征数据
            feature_df = df[behavioral_features].dropna()
            if len(feature_df) == 0:
                print(f"⚠️ {fname} 没有有效数据")
                continue

            # 计算统计特征向量
            sample_vector = []
            for col in behavioral_features:
                col_data = feature_df[col].values
                sample_vector.extend([
                    np.mean(col_data),      # 均值
                    np.std(col_data),       # 标准差
                    np.median(col_data),    # 中位数
                    np.percentile(col_data, 25),  # 25%分位数
                    np.percentile(col_data, 75),  # 75%分位数
                    np.max(col_data) - np.min(col_data),  # 范围
                ])

            all_vectors.append(sample_vector)
            filenames.append(fname)

        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于分析的样本：{folder_path}")
        return None

    # 转换为numpy数组并标准化
    X = np.array(all_vectors)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    print(f"📊 数据形状: {X_scaled.shape}, 样本数: {len(filenames)}")

    # 第二步：Isolation Forest异常检测
    print("🌲 执行Isolation Forest异常检测...")

    iso_forest = IsolationForest(
        contamination=contamination,
        random_state=42,
        n_estimators=200,
        max_samples='auto',
        n_jobs=-1
    )

    iso_predictions = iso_forest.fit_predict(X_scaled)
    iso_scores = iso_forest.decision_function(X_scaled)

    # Isolation Forest异常检测结果 (-1表示异常，1表示正常)
    iso_outliers_mask = iso_predictions == -1
    iso_outliers = [f for f, is_outlier in zip(filenames, iso_outliers_mask) if is_outlier]

    print(f"🌲 Isolation Forest检测异常: {len(iso_outliers)} / {len(filenames)} ({len(iso_outliers)/len(filenames)*100:.1f}%)")

    # 保存结果
    pd.DataFrame({"文件": iso_outliers}).to_csv(output_csv, index=False)
    print(f"📄 异常文件列表已保存: {output_csv}")

    # 保存详细分析结果
    detailed_csv = output_csv.replace('.csv', '_detailed.csv')
    detailed_results = pd.DataFrame({
        "文件": filenames,
        "iso_score": iso_scores,
        "iso_outlier": iso_outliers_mask,
        "is_final_outlier": [f in iso_outliers for f in filenames]
    })
    detailed_results.to_csv(detailed_csv, index=False)
    print(f"📄 详细分析结果已保存: {detailed_csv}")

    print(f"\n✅ 最终异常检测结果: {len(iso_outliers)} / {len(filenames)} ({len(iso_outliers)/len(filenames)*100:.1f}%)")

    return {
        'filenames': filenames,
        'iso_outliers': iso_outliers,
        'final_outliers': iso_outliers,
        'detailed_results': detailed_results,
        'iso_scores': iso_scores,
        'iso_predictions': iso_predictions
    }


def kmeans_isolation_forest_outlier_detection(folder_path, behavioral_features,
                                             n_clusters="auto", distance_threshold=2.5,
                                             contamination=0.08, output_csv="outliers.csv"):
    """
    结合K-means聚类和Isolation Forest进行异常检测的二次筛选。

    参数:
        folder_path (str): 包含 CSV 文件的文件夹路径
        behavioral_features (list): 要提取的行为特征列
        n_clusters (int or "auto"): KMeans 聚类簇数，"auto"为自动选择
        distance_threshold (float): K-means距离阈值倍数
        contamination (float): Isolation Forest异常比例 (0.05-0.2)
        output_csv (str): 输出的异常文件列表 CSV

    返回:
        dict: 包含检测结果的字典
    """
    print(f"🚀 开始异常检测: {os.path.basename(folder_path)}")
    print(f"📋 使用特征: {behavioral_features}")

    # 第一步：数据加载和预处理
    all_vectors = []
    filenames = []

    print("📂 加载数据文件...")
    for fname in tqdm(os.listdir(folder_path), desc="加载数据"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)

            # 检查特征列是否存在
            missing_features = [f for f in behavioral_features if f not in df.columns]
            if missing_features:
                print(f"⚠️ {fname} 缺少特征: {missing_features}")
                continue

            # 提取特征并清理数据
            feature_df = df[behavioral_features]
            feature_df = feature_df.replace([np.inf, -np.inf], np.nan).dropna()

            if feature_df.shape[0] == 0:
                print(f"⚠️ {fname} 清理后无有效数据")
                continue

            # 计算统计特征向量
            sample_vector = []
            for col in behavioral_features:
                col_data = feature_df[col].values
                sample_vector.extend([
                    np.mean(col_data),      # 均值
                    np.std(col_data),       # 标准差
                    np.median(col_data),    # 中位数
                    np.percentile(col_data, 25),  # 25%分位数
                    np.percentile(col_data, 75),  # 75%分位数
                    np.max(col_data) - np.min(col_data),  # 范围
                ])

            all_vectors.append(sample_vector)
            filenames.append(fname)

        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于分析的样本：{folder_path}")
        return None

    # 转换为numpy数组并标准化
    X = np.array(all_vectors)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    print(f"📊 数据形状: {X_scaled.shape}, 样本数: {len(filenames)}")

    # 第二步：K-means聚类异常检测
    print("🎯 执行K-means聚类异常检测...")

    if n_clusters == "auto":
        # 自动选择最优聚类数
        max_k = min(10, len(filenames) // 3)
        if max_k < 2:
            max_k = 2

        silhouette_scores = []
        candidate_k = range(2, max_k + 1)

        for k in candidate_k:
            if k >= len(filenames):
                break
            kmeans_k = KMeans(n_clusters=k, random_state=42, n_init=10).fit(X_scaled)
            score = silhouette_score(X_scaled, kmeans_k.labels_)
            silhouette_scores.append(score)

        if silhouette_scores:
            best_k = candidate_k[np.argmax(silhouette_scores)]
            best_score = max(silhouette_scores)
            print(f"📌 自动选择聚类数: {best_k} (轮廓系数: {best_score:.3f})")
        else:
            best_k = 2
            print(f"📌 使用默认聚类数: {best_k}")
    else:
        best_k = min(n_clusters, len(filenames) - 1)
        print(f"📌 使用指定聚类数: {best_k}")

    # 执行K-means聚类
    kmeans = KMeans(n_clusters=best_k, random_state=42, n_init=10).fit(X_scaled)
    centers = kmeans.cluster_centers_
    labels = kmeans.labels_

    # 计算到聚类中心的距离
    distances = np.linalg.norm(X_scaled - centers[labels], axis=1)
    avg_dist = distances.mean()
    std_dist = distances.std()
    kmeans_threshold = avg_dist + distance_threshold * std_dist

    # K-means异常检测结果
    kmeans_outliers_mask = distances > kmeans_threshold
    kmeans_outliers = [f for f, is_outlier in zip(filenames, kmeans_outliers_mask) if is_outlier]

    print(f"🔍 K-means检测异常: {len(kmeans_outliers)} / {len(filenames)} ({len(kmeans_outliers)/len(filenames)*100:.1f}%)")

    # 第三步：Isolation Forest异常检测
    print("🌲 执行Isolation Forest异常检测...")

    iso_forest = IsolationForest(
        contamination=contamination,
        random_state=42,
        n_estimators=200,
        max_samples='auto',
        n_jobs=-1
    )

    iso_predictions = iso_forest.fit_predict(X_scaled)
    iso_scores = iso_forest.decision_function(X_scaled)

    # Isolation Forest异常检测结果 (-1表示异常，1表示正常)
    iso_outliers_mask = iso_predictions == -1
    iso_outliers = [f for f, is_outlier in zip(filenames, iso_outliers_mask) if is_outlier]

    print(f"🌲 Isolation Forest检测异常: {len(iso_outliers)} / {len(filenames)} ({len(iso_outliers)/len(filenames)*100:.1f}%)")

    # 第四步：结合两种方法进行二次筛选
    print("🔄 结合两种方法进行二次筛选...")

    # 策略1：交集 - 两种方法都认为异常的样本（更保守）
    intersection_outliers = list(set(kmeans_outliers) & set(iso_outliers))

    # 策略2：并集 - 任一方法认为异常的样本（更宽松）
    union_outliers = list(set(kmeans_outliers) | set(iso_outliers))

    # 策略3：加权评分 - 推荐策略
    # 标准化分数并加权组合
    norm_distances = (distances - distances.min()) / (distances.max() - distances.min() + 1e-8)
    norm_iso_scores = (iso_scores.max() - iso_scores) / (iso_scores.max() - iso_scores.min() + 1e-8)

    # 加权组合 (可调整权重)
    kmeans_weight = 0.6
    iso_weight = 0.4
    combined_scores = kmeans_weight * norm_distances + iso_weight * norm_iso_scores

    # 使用组合分数确定最终异常
    score_threshold = np.percentile(combined_scores, 100 * (1 - contamination))
    weighted_outliers = [f for f, score in zip(filenames, combined_scores) if score > score_threshold]

    # 输出统计信息
    print(f"\n� 异常检测结果统计:")
    print(f"   K-means异常: {len(kmeans_outliers)} 个")
    print(f"   Isolation Forest异常: {len(iso_outliers)} 个")
    print(f"   交集策略异常: {len(intersection_outliers)} 个")
    print(f"   并集策略异常: {len(union_outliers)} 个")
    print(f"   加权策略异常: {len(weighted_outliers)} 个")

    # 使用加权策略作为最终结果（推荐）
    final_outliers = weighted_outliers

    print(f"\n✅ 最终异常检测结果: {len(final_outliers)} / {len(filenames)} ({len(final_outliers)/len(filenames)*100:.1f}%)")

    # 保存结果
    pd.DataFrame({"文件": final_outliers}).to_csv(output_csv, index=False)
    print(f"📄 异常文件列表已保存: {output_csv}")

    # 保存详细分析结果
    detailed_csv = output_csv.replace('.csv', '_detailed.csv')
    detailed_results = pd.DataFrame({
        "文件": filenames,
        "kmeans_distance": distances,
        "kmeans_outlier": kmeans_outliers_mask,
        "iso_score": iso_scores,
        "iso_outlier": iso_outliers_mask,
        "combined_score": combined_scores,
        "final_outlier": [f in final_outliers for f in filenames]
    })
    detailed_results.to_csv(detailed_csv, index=False)
    print(f"📄 详细分析结果已保存: {detailed_csv}")

    return {
        'filenames': filenames,
        'kmeans_outliers': kmeans_outliers,
        'iso_outliers': iso_outliers,
        'intersection_outliers': intersection_outliers,
        'union_outliers': union_outliers,
        'weighted_outliers': weighted_outliers,
        'final_outliers': final_outliers,
        'detailed_results': detailed_results
    }

def kmeans_only_outlier_detection(folder_path, behavioral_features, n_clusters="auto", distance_threshold=3.0, output_csv="kmeans_outliers.csv"):
    """
    仅使用K-means聚类进行异常检测
    """
    print(f"🚀 开始K-means异常检测: {os.path.basename(folder_path)}")
    print(f"📋 使用特征: {behavioral_features}")

    # 数据加载和预处理
    all_vectors = []
    filenames = []

    print("📂 加载数据文件...")
    for fname in tqdm(os.listdir(folder_path), desc="加载数据"):
        if not fname.endswith(".csv"):
            continue
        csv_path = os.path.join(folder_path, fname)
        try:
            df = pd.read_csv(csv_path)

            # 检查特征列是否存在
            missing_features = [f for f in behavioral_features if f not in df.columns]
            if missing_features:
                print(f"⚠️ {fname} 缺少特征: {missing_features}")
                continue

            # 提取特征数据
            feature_df = df[behavioral_features].dropna()
            if len(feature_df) == 0:
                print(f"⚠️ {fname} 没有有效数据")
                continue

            # 计算统计特征向量
            sample_vector = []
            for col in behavioral_features:
                col_data = feature_df[col].values
                sample_vector.extend([
                    np.mean(col_data),      # 均值
                    np.std(col_data),       # 标准差
                    np.median(col_data),    # 中位数
                    np.percentile(col_data, 25),  # 25%分位数
                    np.percentile(col_data, 75),  # 75%分位数
                    np.max(col_data) - np.min(col_data),  # 范围
                ])

            all_vectors.append(sample_vector)
            filenames.append(fname)

        except Exception as e:
            print(f"⚠️ 读取失败: {fname}, 原因: {e}")

    if not all_vectors:
        print(f"❌ 没有可用于分析的样本：{folder_path}")
        return None

    # 转换为numpy数组并标准化
    X = np.array(all_vectors)
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    print(f"📊 数据形状: {X_scaled.shape}, 样本数: {len(filenames)}")

    # K-means聚类异常检测
    print("🎯 执行K-means聚类异常检测...")

    # 自动选择最佳聚类数
    if n_clusters == "auto":
        best_k = 2
        best_score = -1
        for k in range(2, min(11, len(filenames)//10 + 2)):
            try:
                kmeans_temp = KMeans(n_clusters=k, random_state=42, n_init=10).fit(X_scaled)
                score = silhouette_score(X_scaled, kmeans_temp.labels_)
                if score > best_score:
                    best_score = score
                    best_k = k
            except:
                continue
        print(f"📌 自动选择聚类数: {best_k} (轮廓系数: {best_score:.3f})")
    else:
        best_k = n_clusters

    # 执行K-means聚类
    kmeans = KMeans(n_clusters=best_k, random_state=42, n_init=10).fit(X_scaled)
    centers = kmeans.cluster_centers_
    labels = kmeans.labels_

    # 计算到聚类中心的距离
    distances = np.linalg.norm(X_scaled - centers[labels], axis=1)
    avg_dist = distances.mean()
    std_dist = distances.std()
    kmeans_threshold = avg_dist + distance_threshold * std_dist

    # K-means异常检测结果
    kmeans_outliers_mask = distances > kmeans_threshold
    kmeans_outliers = [f for f, is_outlier in zip(filenames, kmeans_outliers_mask) if is_outlier]

    print(f"🔍 K-means检测异常: {len(kmeans_outliers)} / {len(filenames)} ({len(kmeans_outliers)/len(filenames)*100:.1f}%)")

    # 保存结果
    pd.DataFrame({"文件": kmeans_outliers}).to_csv(output_csv, index=False)
    print(f"📄 异常文件列表已保存: {output_csv}")

    # 保存详细分析结果
    detailed_csv = output_csv.replace('.csv', '_detailed.csv')
    detailed_results = pd.DataFrame({
        "文件": filenames,
        "kmeans_distance": distances,
        "kmeans_outlier": kmeans_outliers_mask,
        "is_final_outlier": [f in kmeans_outliers for f in filenames]
    })
    detailed_results.to_csv(detailed_csv, index=False)
    print(f"📄 详细分析结果已保存: {detailed_csv}")

    print(f"\n✅ 最终异常检测结果: {len(kmeans_outliers)} / {len(filenames)} ({len(kmeans_outliers)/len(filenames)*100:.1f}%)")

    return {
        'filenames': filenames,
        'kmeans_outliers': kmeans_outliers,
        'final_outliers': kmeans_outliers,
        'detailed_results': detailed_results,
        'distances': distances,
        'kmeans_threshold': kmeans_threshold
    }


# 为了向后兼容，保留原函数名的别名
def global_kmeans_filter(folder_path, behavioral_features, n_clusters="auto", distance_threshold=3.0, output_csv="global_kmeans_outliers.csv"):
    """
    原始K-means异常检测函数（向后兼容）
    """
    return kmeans_only_outlier_detection(
        folder_path=folder_path,
        behavioral_features=behavioral_features,
        n_clusters=n_clusters,
        distance_threshold=distance_threshold,
        output_csv=output_csv
    )

if __name__ == "__main__":
    # 配置参数
    behavioral_features = ["pose_Tx", "pose_Ty", "AU17_r", "AU26_r"]  # 根据需要修改特征名

    # 数据路径
    train_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Train"
    val_path = "/data/gsd/ywj/dataset/EngageNet_Processed_CSV/Validation"

    print("=" * 70)
    print("🚀 K-means + Isolation Forest 异常检测系统")
    print("=" * 70)
    print(f"📋 使用特征: {behavioral_features}")
    print(f"📂 训练数据路径: {train_path}")
    print(f"� 验证数据路径: {val_path}")

    # 检查数据路径
    if not os.path.exists(train_path):
        print(f"❌ 训练数据路径不存在: {train_path}")
        exit(1)

    if not os.path.exists(val_path):
        print(f"❌ 验证数据路径不存在: {val_path}")
        exit(1)

    # 训练集异常检测
    print(f"\n🔍 处理训练集...")
    train_results = kmeans_isolation_forest_outlier_detection(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.04,  # 预期异常比例4% (降低筛选强度)
        output_csv="train_outliers.csv"
    )

    # 验证集异常检测
    print(f"\n🔍 处理验证集...")
    val_results = kmeans_isolation_forest_outlier_detection(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.04,  # 预期异常比例4% (降低筛选强度)
        output_csv="val_outliers.csv"
    )

    # 汇总结果
    print("\n� 最终结果汇总")
    print("=" * 70)

    if train_results and val_results:
        print(f"训练集:")
        print(f"  总文件数: {len(train_results['filenames'])}")
        print(f"  K-means异常: {len(train_results['kmeans_outliers'])}")
        print(f"  Isolation Forest异常: {len(train_results['iso_outliers'])}")
        print(f"  最终异常: {len(train_results['final_outliers'])}")
        print(f"  异常比例: {len(train_results['final_outliers'])/len(train_results['filenames'])*100:.1f}%")

        print(f"\n验证集:")
        print(f"  总文件数: {len(val_results['filenames'])}")
        print(f"  K-means异常: {len(val_results['kmeans_outliers'])}")
        print(f"  Isolation Forest异常: {len(val_results['iso_outliers'])}")
        print(f"  最终异常: {len(val_results['final_outliers'])}")
        print(f"  异常比例: {len(val_results['final_outliers'])/len(val_results['filenames'])*100:.1f}%")

        print(f"\n📄 输出文件:")
        print(f"  训练集异常列表: train_outliers.csv")
        print(f"  验证集异常列表: val_outliers.csv")
        print(f"  训练集详细分析: train_outliers_detailed.csv")
        print(f"  验证集详细分析: val_outliers_detailed.csv")

    else:
        print("❌ 异常检测过程中出现错误")

    print("\n✅ 异常检测完成!")
    print("💡 建议:")
    print("   1. 查看详细分析文件了解每个样本的异常分数")
    print("   2. 根据实际需求调整contamination参数")
    print("   3. 在训练时使用异常列表文件排除异常样本")


# 为了向后兼容，添加函数别名
def kmeans_isolation_forest_filter(folder_path, behavioral_features,
                                  n_clusters="auto", distance_threshold=2.5,
                                  contamination=0.08, output_csv="outliers.csv"):
    """
    向后兼容的函数别名，现在只使用Isolation Forest
    """
    print("⚠️ 注意：现在只使用Isolation Forest进行异常检测")
    return isolation_forest_outlier_detection(
        folder_path=folder_path,
        behavioral_features=behavioral_features,
        contamination=contamination,
        output_csv=output_csv
    )
