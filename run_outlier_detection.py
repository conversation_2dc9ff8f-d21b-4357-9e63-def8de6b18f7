#!/usr/bin/env python3
"""
运行异常检测的主脚本
结合K-means和Isolation Forest进行数据预处理和异常检测
"""

import os
import sys
import pandas as pd
from data.kmeans_isolation_outlier_detection import (
    kmeans_isolation_forest_filter,
    global_kmeans_filter,
    isolation_forest_outlier_detection
)
from outlier_detection_config import (
    ISOLATION_FOREST_CONFIG,
    KMEANS_CONFIG,
    BEHAVIORAL_FEATURES,
    DATA_PATHS,
    OUTPUT_FILES,
    print_config_summary
)

def main():
    """主函数：执行异常检测流程"""
    
    print("🚀 TCCT-Net 数据异常检测系统")
    print("=" * 60)

    # 显示当前配置
    print_config_summary()

    # 使用配置文件中的参数
    behavioral_features = BEHAVIORAL_FEATURES
    train_path = DATA_PATHS['train_path']
    val_path = DATA_PATHS['val_path']
    
    # 检查数据路径是否存在
    if not os.path.exists(train_path):
        print(f"❌ 训练数据路径不存在: {train_path}")
        print("请检查数据路径配置")
        return
    
    if not os.path.exists(val_path):
        print(f"❌ 验证数据路径不存在: {val_path}")
        print("请检查数据路径配置")
        return
    
    print(f"📂 训练数据路径: {train_path}")
    print(f"📂 验证数据路径: {val_path}")
    print(f"📋 使用特征: {behavioral_features}")
    
    # 选择检测方法
    print("\n🔧 选择异常检测方法:")
    print("1. 仅使用K-means聚类检测")
    print("2. 仅使用Isolation Forest检测 (推荐)")
    print("3. 使用K-means + Isolation Forest二次筛选")
    print("4. 运行多种方法并对比结果")

    choice = input("请选择 (1/2/3/4) [默认: 2]: ").strip()
    if not choice:
        choice = "2"

    try:
        if choice == "1":
            run_kmeans_only(train_path, val_path, behavioral_features)
        elif choice == "2":
            run_isolation_forest_only(train_path, val_path, behavioral_features)
        elif choice == "3":
            run_combined_detection(train_path, val_path, behavioral_features)
        elif choice == "4":
            run_comparison(train_path, val_path, behavioral_features)
        else:
            print("❌ 无效选择，使用默认方法 (仅Isolation Forest)")
            run_isolation_forest_only(train_path, val_path, behavioral_features)
            
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def run_kmeans_only(train_path, val_path, behavioral_features):
    """仅使用K-means聚类检测"""
    print("\n📊 执行K-means聚类异常检测")
    print("-" * 40)

    # 训练集
    print("🔍 处理训练集...")
    global_kmeans_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        output_csv="train_kmeans_outliers.csv"
    )

    # 验证集
    print("🔍 处理验证集...")
    global_kmeans_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        output_csv="val_kmeans_outliers.csv"
    )

    print("\n✅ K-means异常检测完成!")
    print("📄 结果文件: train_kmeans_outliers.csv, val_kmeans_outliers.csv")

def run_isolation_forest_only(train_path, val_path, behavioral_features):
    """仅使用Isolation Forest检测"""
    print("\n🌲 执行Isolation Forest异常检测")
    print("-" * 40)

    # 训练集
    print("🔍 处理训练集...")
    train_results = isolation_forest_outlier_detection(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        contamination=ISOLATION_FOREST_CONFIG['contamination'],
        output_csv=OUTPUT_FILES['train_isolation']
    )

    # 验证集
    print("🔍 处理验证集...")
    val_results = isolation_forest_outlier_detection(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        contamination=ISOLATION_FOREST_CONFIG['contamination'],
        output_csv=OUTPUT_FILES['val_isolation']
    )

    print("\n✅ Isolation Forest异常检测完成!")
    print("📄 主要结果文件: train_isolation_outliers.csv, val_isolation_outliers.csv")
    print("📄 详细分析文件: train_isolation_outliers_detailed.csv, val_isolation_outliers_detailed.csv")
    print("💡 建议在训练时使用主要结果文件作为排除列表")

    # 输出统计信息
    if train_results and val_results:
        print(f"\n📊 检测结果统计:")
        print(f"   训练集异常: {len(train_results['final_outliers'])} 个")
        print(f"   验证集异常: {len(val_results['final_outliers'])} 个")
        total_train = len(train_results['filenames'])
        total_val = len(val_results['filenames'])
        print(f"   训练集异常比例: {len(train_results['final_outliers'])/total_train*100:.1f}%")
        print(f"   验证集异常比例: {len(val_results['final_outliers'])/total_val*100:.1f}%")

def run_combined_detection(train_path, val_path, behavioral_features):
    """使用K-means + Isolation Forest二次筛选"""
    print("\n🌲 执行K-means + Isolation Forest二次筛选")
    print("-" * 50)
    
    # 训练集
    print("🔍 处理训练集...")
    train_results = kmeans_isolation_forest_filter(
        folder_path=train_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.04,  # 预期4%的异常比例 (降低筛选强度)
        output_csv="train_combined_outliers.csv"
    )

    # 验证集
    print("🔍 处理验证集...")
    val_results = kmeans_isolation_forest_filter(
        folder_path=val_path,
        behavioral_features=behavioral_features,
        n_clusters="auto",
        distance_threshold=2.5,
        contamination=0.04,  # 预期4%的异常比例 (降低筛选强度)
        output_csv="val_combined_outliers.csv"
    )
    
    print("\n✅ 二次筛选异常检测完成!")
    print("📄 主要结果文件: train_combined_outliers.csv, val_combined_outliers.csv")
    print("📄 详细分析文件: train_combined_detailed.csv, val_combined_detailed.csv")
    print("💡 建议在训练时使用主要结果文件作为排除列表")

def run_comparison(train_path, val_path, behavioral_features):
    """运行多种方法并对比结果"""
    print("\n📊 运行方法对比分析")
    print("-" * 40)

    # 先运行K-means
    print("1️⃣ 执行K-means检测...")
    run_kmeans_only(train_path, val_path, behavioral_features)

    # 运行Isolation Forest
    print("\n2️⃣ 执行Isolation Forest检测...")
    run_isolation_forest_only(train_path, val_path, behavioral_features)

    # 再运行二次筛选
    print("\n3️⃣ 执行二次筛选...")
    run_combined_detection(train_path, val_path, behavioral_features)
    
    # 对比分析
    print("\n📈 结果对比分析")
    print("=" * 40)
    
    try:
        # 读取结果文件
        train_kmeans = pd.read_csv("train_kmeans_outliers.csv")
        val_kmeans = pd.read_csv("val_kmeans_outliers.csv")
        train_isolation = pd.read_csv("train_isolation_outliers.csv")
        val_isolation = pd.read_csv("val_isolation_outliers.csv")
        train_combined = pd.read_csv("train_combined_outliers.csv")
        val_combined = pd.read_csv("val_combined_outliers.csv")

        print(f"训练集对比:")
        print(f"  K-means异常数: {len(train_kmeans)}")
        print(f"  Isolation Forest异常数: {len(train_isolation)}")
        print(f"  二次筛选异常数: {len(train_combined)}")

        print(f"\n验证集对比:")
        print(f"  K-means异常数: {len(val_kmeans)}")
        print(f"  Isolation Forest异常数: {len(val_isolation)}")
        print(f"  二次筛选异常数: {len(val_combined)}")
        
        # 分析重叠情况
        train_kmeans_set = set(train_kmeans['文件'].values)
        train_isolation_set = set(train_isolation['文件'].values)
        train_combined_set = set(train_combined['文件'].values)

        val_kmeans_set = set(val_kmeans['文件'].values)
        val_isolation_set = set(val_isolation['文件'].values)
        val_combined_set = set(val_combined['文件'].values)

        # 计算重叠
        train_kmeans_isolation_overlap = len(train_kmeans_set & train_isolation_set)
        val_kmeans_isolation_overlap = len(val_kmeans_set & val_isolation_set)

        print(f"\n重叠分析:")
        print(f"  训练集K-means与Isolation Forest重叠: {train_kmeans_isolation_overlap}")
        print(f"  验证集K-means与Isolation Forest重叠: {val_kmeans_isolation_overlap}")

        # 保存对比报告
        comparison_report = {
            "数据集": ["训练集", "验证集"],
            "K-means异常数": [len(train_kmeans), len(val_kmeans)],
            "Isolation Forest异常数": [len(train_isolation), len(val_isolation)],
            "二次筛选异常数": [len(train_combined), len(val_combined)],
            "K-means与Isolation Forest重叠": [train_kmeans_isolation_overlap, val_kmeans_isolation_overlap]
        }
        
        comparison_df = pd.DataFrame(comparison_report)
        comparison_df.to_csv("outlier_detection_comparison.csv", index=False)
        print(f"\n📄 对比报告已保存: outlier_detection_comparison.csv")
        
    except Exception as e:
        print(f"⚠️ 对比分析失败: {e}")
    
    print("\n💡 建议:")
    print("   - 推荐使用Isolation Forest结果，筛选更精准")
    print("   - 如果需要更保守的筛选，可以使用K-means结果")
    print("   - 如果追求最严格的数据质量，使用二次筛选结果")
    print("   - 可以查看详细分析文件来了解具体的异常模式")

if __name__ == "__main__":
    main()
