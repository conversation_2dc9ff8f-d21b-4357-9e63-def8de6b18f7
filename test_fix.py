#!/usr/bin/env python3
"""
测试修复后的异常检测功能
"""

def test_imports():
    """测试所有导入是否正常"""
    try:
        from data.kmeans_isolation_outlier_detection import (
            global_kmeans_filter,
            isolation_forest_outlier_detection,
            kmeans_only_outlier_detection
        )
        print("✅ 异常检测函数导入成功")
        
        from outlier_detection_config import (
            ISOLATION_FOREST_CONFIG,
            BEHAVIORAL_FEATURES,
            DATA_PATHS
        )
        print("✅ 配置文件导入成功")
        
        print(f"📋 当前contamination设置: {ISOLATION_FOREST_CONFIG['contamination']}")
        print(f"📋 使用特征: {BEHAVIORAL_FEATURES}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试修复后的异常检测系统")
    print("=" * 40)
    
    if test_imports():
        print("\n✅ 所有导入测试通过!")
        print("💡 现在可以安全运行: python run_outlier_detection.py")
    else:
        print("\n❌ 导入测试失败，请检查代码")
